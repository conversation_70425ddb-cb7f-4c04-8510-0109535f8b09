"""
YouTube Competitor Analysis Tool - Main Application

A Flask web application for tracking and analyzing YouTube videos and channels.
This is the main controller that handles HTTP routes and coordinates between modules.
"""

import time
import csv
import io
import json
import threading
from datetime import datetime, timezone
from flask import Flask, render_template, request, redirect, url_for, Response, session

# Import our custom modules
from src.data_manager import (
    load_data, save_data, load_video_cache, save_video_cache,
    find_video_by_id, find_group_by_id, create_group, filter_videos_by_group, filter_videos_by_channel,
    load_channel_data, save_channel_data, find_channel_by_name,
    is_channel_data_stale, get_unique_channels_from_videos,
    get_canonical_channel_identifiers_from_videos, purge_stale_channels_from_cache
)
from src.scraper import get_video_id, scrape_youtube_video_to_object, scrape_channel_data, extract_channel_id_from_url, background_scrape_and_save
from src.utils import prepare_videos_for_display

# Initialize the Flask app
app = Flask(__name__)

# Configure Flask sessions for state persistence
app.secret_key = 'youtube-competitor-tool-secret-key-change-in-production'


# --- FLASK ROUTES ---

@app.route('/')
def index():
    """Main page - BLAZING FAST! No network requests, pure cache reading with groups support."""
    # Load complete data structure (NO SCRAPING!)
    data = load_data()
    videos = data['videos']
    groups = data['groups']

    # PHASE 1: PRE-CALCULATE VIDEO COUNTS FOR EACH GROUP
    # This eliminates the need for complex template logic and improves performance
    enriched_groups = []
    for group in groups:
        # Create a copy of the group to avoid modifying the original
        enriched_group = group.copy()
        # Calculate how many videos belong to this group
        video_count = sum(1 for video in videos if group['id'] in video.get('groups', []))
        # Add the pre-calculated count to the group object
        enriched_group['video_count'] = video_count
        enriched_groups.append(enriched_group)

    # Get filtering and sorting preferences with session persistence
    # If parameters are provided in URL, store them in session and use them
    if request.args.get('sort_by'):
        session['video_sort_by'] = request.args.get('sort_by')
    if request.args.get('group_filter'):
        session['video_group_filter'] = request.args.get('group_filter')

    # Use session values as defaults, with fallbacks
    group_filter = request.args.get('group_filter') or session.get('video_group_filter', 'all')
    channel_filter = request.args.get('channel_id', 'all')  # Channel filtering (no session persistence for now)
    sort_by = request.args.get('sort_by') or session.get('video_sort_by', 'date_added_newest')

    # Apply filtering (INSTANT - all data is local!)
    filtered_videos = videos

    # Apply group filtering first
    if group_filter and group_filter != 'all':
        filtered_videos = filter_videos_by_group(filtered_videos, group_filter)

    # Apply channel filtering second (can be combined with group filtering)
    if channel_filter and channel_filter != 'all':
        filtered_videos = filter_videos_by_channel(filtered_videos, channel_filter)

    # Prepare videos for display (updates timeAgo and adds group names)
    filtered_videos = prepare_videos_for_display(filtered_videos, groups)

    # Apply sorting logic (INSTANT - all data is local!)
    if sort_by == 'date_added_oldest':
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('dateAdded', ''))
    elif sort_by == 'popular':
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('viewCountRaw', 0), reverse=True)
    elif sort_by == 'date_published_newest':
        # Sort by publish date, handling None values
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('publishDateRaw') or '', reverse=True)
    elif sort_by == 'date_published_oldest':
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('publishDateRaw') or 'z')
    elif sort_by == 'alphabetical':
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('title', '').lower())
    elif sort_by == 'channel':
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('channelName', '').lower())
    else:  # 'date_added_newest' - default
        filtered_videos = sorted(filtered_videos, key=lambda x: x.get('dateAdded', ''), reverse=True)

    # Check if there are any pending videos (for auto-refresh)
    has_pending_videos = any(video.get('status') == 'pending' for video in filtered_videos)

    return render_template('index.html',
                         videos=filtered_videos,
                         groups=enriched_groups,  # Pass enriched groups with video_count
                         sort_by=sort_by,
                         group_filter=group_filter,
                         channel_filter=channel_filter,
                         has_pending_videos=has_pending_videos)


@app.route('/add_video', methods=['POST'])
def add_video():
    """
    REVOLUTIONARY ASYNCHRONOUS VIDEO ADDITION!

    This route now provides INSTANT user feedback by:
    1. Creating placeholder objects immediately
    2. Launching background threads for scraping
    3. Redirecting user instantly (no waiting!)

    The user sees their videos appear immediately as "Scraping..."
    and they get updated with real data as background workers complete.
    """
    video_urls = request.form.get('video_url', '').strip()

    # Preserve sorting state
    current_sort_by = request.form.get('sort_by') or session.get('video_sort_by', 'date_added_newest')
    current_group_filter = request.form.get('group_filter') or session.get('video_group_filter', 'all')

    if not video_urls:
        return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))

    # Load existing cache
    videos = load_video_cache()

    # Split by lines to support bulk adding
    url_lines = video_urls.splitlines()
    new_placeholders_added = 0
    background_threads = []

    print(f"🚀 Starting asynchronous video addition for {len(url_lines)} URLs...")

    for line in url_lines:
        line = line.strip()
        if not line:
            continue

        video_id = get_video_id(line)
        if not video_id:
            print(f"⚠️ Could not extract video ID from: {line}")
            continue

        # Check if video already exists in cache
        if find_video_by_id(videos, video_id):
            print(f"📋 Video {video_id} already exists in cache, skipping...")
            continue

        # CREATE PLACEHOLDER OBJECT (INSTANT!)
        now = datetime.now(timezone.utc)
        placeholder = {
            'id': video_id,
            'title': 'Scraping...',
            'url': f'https://www.youtube.com/watch?v={video_id}',
            'channelName': 'Loading...',
            'channelId': '',
            'channelIconUrl': 'https://www.youtube.com/img/desktop/yt_1200.png',
            'views': 'Loading...',
            'viewCountRaw': 0,
            'uploadDate': 'Loading...',
            'publishDateRaw': '',
            'timeAgo': '',
            'length': '0:00',
            'thumbnail': f'https://i.ytimg.com/vi/{video_id}/hqdefault.jpg',
            'groups': [],
            'dateAdded': now.isoformat(),
            'lastUpdated': now.isoformat(),
            'status': 'pending'  # KEY: This marks it as being processed
        }

        # Add placeholder to the beginning of the list (newest first)
        videos.insert(0, placeholder)
        new_placeholders_added += 1
        print(f"📝 Created placeholder for: {video_id}")

        # LAUNCH BACKGROUND THREAD (NON-BLOCKING!)
        thread = threading.Thread(
            target=background_scrape_and_save,
            args=(video_id,),
            daemon=True  # Thread dies when main program exits
        )
        thread.start()
        background_threads.append(thread)
        print(f"🔄 Launched background worker for: {video_id}")

    # SAVE PLACEHOLDERS IMMEDIATELY (INSTANT UI UPDATE!)
    if new_placeholders_added > 0:
        save_video_cache(videos)
        print(f"✅ Added {new_placeholders_added} placeholder(s) to cache!")
        print(f"🔄 {len(background_threads)} background workers are now scraping...")

    # INSTANT REDIRECT - User sees placeholders immediately!
    return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))


@app.route('/remove_video/<video_id>')
def remove_video(video_id):
    """INSTANT removal from cache - no network requests!"""
    videos = load_video_cache()

    # Find and remove the video object
    videos = [video for video in videos if video.get('id') != video_id]

    # Save updated cache
    save_video_cache(videos)
    print(f"Removed video {video_id} from cache")

    # Preserve sorting state
    current_sort_by = request.args.get('sort_by', 'date_added_newest')
    current_group_filter = request.args.get('group_filter', 'all')

    return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))


@app.route('/refresh_all')
def refresh_all():
    """User-triggered refresh of all video data. This is intentionally slow."""
    videos = load_video_cache()

    # Preserve sorting state
    current_sort_by = request.args.get('sort_by', 'date_added_newest')
    current_group_filter = request.args.get('group_filter', 'all')

    if not videos:
        return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))

    print(f"Refreshing data for {len(videos)} videos...")
    updated_videos = []

    for i, video in enumerate(videos):
        video_id = video.get('id')
        if not video_id:
            updated_videos.append(video)
            continue

        print(f"Refreshing {i+1}/{len(videos)}: {video_id}")

        # Re-scrape the video
        fresh_data = scrape_youtube_video_to_object(video_id)

        if fresh_data:
            # Preserve the original dateAdded
            fresh_data['dateAdded'] = video.get('dateAdded', fresh_data['dateAdded'])
            updated_videos.append(fresh_data)
        else:
            # Keep old data if scraping fails
            updated_videos.append(video)

        # Rate limiting
        if i < len(videos) - 1:
            time.sleep(1)

    # Save refreshed cache
    save_video_cache(updated_videos)
    print("Refresh complete!")

    return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))


@app.route('/export/<format>')
def export_data(format):
    """Export video data in CSV or JSON format."""
    data = load_data()
    videos = data['videos']

    if format == 'json':
        # JSON Export - return the complete data structure
        response = Response(
            json.dumps(data, indent=2),
            mimetype='application/json',
            headers={'Content-Disposition': 'attachment; filename=youtube_videos_export.json'}
        )
        return response

    elif format == 'csv':
        # CSV Export - create CSV from video data
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header row
        writer.writerow([
            'ID', 'Title', 'URL', 'Channel Name', 'Views (Formatted)', 'Views (Raw)',
            'Upload Date', 'Upload Date (Raw)', 'Length', 'Groups', 'Date Added', 'Last Updated'
        ])

        # Write video data rows
        for video in videos:
            writer.writerow([
                video.get('id', ''),
                video.get('title', ''),
                video.get('url', ''),
                video.get('channelName', ''),
                video.get('views', ''),
                video.get('viewCountRaw', 0),
                video.get('uploadDate', ''),
                video.get('publishDateRaw', ''),
                video.get('length', ''),
                ', '.join(video.get('groups', [])),
                video.get('dateAdded', ''),
                video.get('lastUpdated', '')
            ])

        # Create response
        response = Response(
            output.getvalue(),
            mimetype='text/csv',
            headers={'Content-Disposition': 'attachment; filename=youtube_videos_export.csv'}
        )
        return response

    else:
        # Preserve sorting state for invalid export format
        current_sort_by = request.args.get('sort_by', 'date_added_newest')
        current_group_filter = request.args.get('group_filter', 'all')
        return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))


@app.route('/create_group', methods=['POST'])
def create_group_route():
    """Create a new group."""
    group_name = request.form.get('group_name', '').strip()

    # Preserve sorting state
    current_sort_by = request.form.get('sort_by') or request.args.get('sort_by', 'date_added_newest')
    current_group_filter = request.form.get('group_filter') or request.args.get('group_filter', 'all')

    if not group_name:
        return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))

    data = load_data()
    new_group = create_group(group_name)
    data['groups'].append(new_group)
    save_data(data)

    print(f"Created new group: {group_name}")
    return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))


@app.route('/delete_group/<group_id>')
def delete_group_route(group_id):
    """Delete a group and remove it from all videos."""
    data = load_data()

    # Remove the group from the groups list
    data['groups'] = [group for group in data['groups'] if group.get('id') != group_id]

    # Remove the group from all videos
    for video in data['videos']:
        if group_id in video.get('groups', []):
            video['groups'].remove(group_id)

    save_data(data)
    print(f"Deleted group: {group_id}")

    # Preserve sorting state
    current_sort_by = request.args.get('sort_by', 'date_added_newest')
    current_group_filter = request.args.get('group_filter', 'all')

    return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))


@app.route('/update_video_groups', methods=['POST'])
def update_video_groups():
    """Update the groups for a specific video."""
    video_id = request.form.get('video_id')
    selected_groups = request.form.getlist('groups')  # List of selected group IDs

    # Preserve sorting state
    current_sort_by = request.form.get('sort_by') or request.args.get('sort_by', 'date_added_newest')
    current_group_filter = request.form.get('group_filter') or request.args.get('group_filter', 'all')

    if not video_id:
        return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))

    data = load_data()
    video = find_video_by_id(data['videos'], video_id)

    if video:
        video['groups'] = selected_groups
        save_data(data)
        print(f"Updated groups for video {video_id}: {selected_groups}")

    return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))


@app.route('/channels')
def channels():
    """
    Channels dashboard with STRICT DATA INTEGRITY.

    Implements single source of truth: only channels that exist in videos are displayed.
    Automatically purges stale channel data to maintain consistency.
    """
    # Load video data to establish canonical channel list
    data = load_data()
    videos = data['videos']

    # STEP 1: Create canonical list of channel identifiers from videos (SINGLE SOURCE OF TRUTH)
    canonical_identifiers = get_canonical_channel_identifiers_from_videos(videos)

    if not canonical_identifiers:
        # No channels to display
        return render_template('channels.html', channels=[], sort_by='name')

    # STEP 2: Load and clean channel cache (PURGE STALE DATA)
    channel_cache = load_channel_data()

    # Purge channels that no longer exist in any videos
    cleaned_cache = purge_stale_channels_from_cache(channel_cache, canonical_identifiers)

    # Save cleaned cache if changes were made
    if len(cleaned_cache) != len(channel_cache):
        save_channel_data(cleaned_cache)
        print(f"Purged {len(channel_cache) - len(cleaned_cache)} stale channels from cache")

    channel_cache = cleaned_cache

    # STEP 3: Identify channels that need scraping (missing or stale)
    channels_to_scrape = []
    for identifier, channel_name in canonical_identifiers:
        channel = find_channel_by_name(channel_cache, channel_name)
        if not channel or is_channel_data_stale(channel):
            channels_to_scrape.append(channel_name)

    # STEP 4: Scrape missing or stale channel data
    if channels_to_scrape:
        print(f"Scraping data for {len(channels_to_scrape)} channels...")
        for channel_name in channels_to_scrape:
            print(f"Scraping channel: {channel_name}")
            channel_data = scrape_channel_data(channel_name)
            if channel_data:
                # Use channel name as key for easy lookup
                channel_cache[channel_name] = channel_data
                time.sleep(1)  # Rate limiting

        # Save updated cache
        save_channel_data(channel_cache)
        print("Channel data scraping complete!")

    # STEP 5: Prepare channels list for display (ONLY CANONICAL CHANNELS)
    channels_list = []
    for identifier, channel_name in canonical_identifiers:
        channel = find_channel_by_name(channel_cache, channel_name)
        if channel:
            # Count videos for this channel using robust matching
            video_count = 0
            for video in videos:
                video_channel_id = video.get('channelId', '')
                video_channel_name = video.get('channelName', '')

                # Match by channelId (preferred) or channelName (fallback)
                if (video_channel_id and video_channel_id == identifier) or \
                   (video_channel_name == channel_name):
                    video_count += 1

            channel_display = channel.copy()
            channel_display['videoCount'] = video_count
            channels_list.append(channel_display)

    # Get sorting preference with session persistence
    if request.args.get('sort_by'):
        session['channel_sort_by'] = request.args.get('sort_by')

    sort_by = request.args.get('sort_by') or session.get('channel_sort_by', 'name')

    # Apply sorting
    if sort_by == 'subscribers':
        channels_list = sorted(channels_list, key=lambda x: x.get('subscriberCountRaw', 0), reverse=True)
    elif sort_by == 'videos':
        channels_list = sorted(channels_list, key=lambda x: x.get('videoCount', 0), reverse=True)
    else:  # 'name' - default
        channels_list = sorted(channels_list, key=lambda x: x.get('name', '').lower())

    return render_template('channels.html', channels=channels_list, sort_by=sort_by)


@app.route('/add_channel', methods=['POST'])
def add_channel():
    """Add a channel manually by URL to the tracking list."""
    channel_url = request.form.get('channel_url', '').strip()

    # Preserve sorting state
    current_sort_by = request.form.get('sort_by') or session.get('channel_sort_by', 'name')

    if not channel_url:
        return redirect(url_for('channels', sort_by=current_sort_by))

    # Extract channel identifier from URL
    channel_identifier = extract_channel_id_from_url(channel_url)
    if not channel_identifier:
        print(f"Could not extract channel identifier from URL: {channel_url}")
        return redirect(url_for('channels', sort_by=current_sort_by))

    # Load existing channel cache
    channel_cache = load_channel_data()

    # Check if channel already exists (by identifier or name)
    existing_channel = None
    for cache_key, channel_data in channel_cache.items():
        if (channel_data.get('channelId') == channel_identifier or
            channel_data.get('name') == channel_identifier or
            cache_key == channel_identifier):
            existing_channel = channel_data
            break

    if existing_channel:
        print(f"Channel already exists: {existing_channel.get('name', channel_identifier)}")
        return redirect(url_for('channels', sort_by=current_sort_by))

    # Scrape the new channel
    print(f"Adding new channel: {channel_identifier}")
    channel_data = scrape_channel_data(channel_identifier)

    if channel_data:
        # Mark as manually added
        channel_data['manuallyAdded'] = True

        # Use channel name as key for easy lookup
        channel_name = channel_data.get('name', channel_identifier)
        channel_cache[channel_name] = channel_data

        # Save updated cache
        save_channel_data(channel_cache)
        print(f"Successfully added channel: {channel_name}")
    else:
        print(f"Failed to scrape channel data for: {channel_identifier}")

    return redirect(url_for('channels', sort_by=current_sort_by))


@app.route('/import_data', methods=['POST'])
def import_data():
    """Import video data from JSON or CSV files with smart merging."""
    # Preserve sorting state
    current_sort_by = request.form.get('sort_by') or request.args.get('sort_by', 'date_added_newest')
    current_group_filter = request.form.get('group_filter') or request.args.get('group_filter', 'all')

    if 'import_file' not in request.files:
        print("No file uploaded")
        return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))

    file = request.files['import_file']
    if file.filename == '':
        print("No file selected")
        return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))

    # Load existing data
    existing_data = load_data()
    existing_videos = existing_data['videos']
    existing_groups = existing_data['groups']

    try:
        file_content = file.read().decode('utf-8')
        filename = file.filename.lower()

        imported_videos = []
        imported_groups = []

        if filename.endswith('.json'):
            # JSON Import Logic
            print("Processing JSON import...")
            imported_data = json.loads(file_content)

            # Handle different JSON structures
            if isinstance(imported_data, dict):
                imported_videos = imported_data.get('videos', [])
                imported_groups = imported_data.get('groups', [])
            elif isinstance(imported_data, list):
                # Assume it's a list of videos
                imported_videos = imported_data

        elif filename.endswith('.csv'):
            # CSV Import Logic
            print("Processing CSV import...")
            import csv
            from io import StringIO

            csv_reader = csv.DictReader(StringIO(file_content))
            for row in csv_reader:
                # Convert CSV row back to video object structure
                video_obj = {
                    'id': row.get('ID', ''),
                    'title': row.get('Title', ''),
                    'url': row.get('URL', ''),
                    'channelName': row.get('Channel Name', ''),
                    'views': row.get('Views (Formatted)', ''),
                    'viewCountRaw': int(row.get('Views (Raw)', 0) or 0),
                    'uploadDate': row.get('Upload Date', ''),
                    'publishDateRaw': row.get('Upload Date (Raw)', ''),
                    'length': row.get('Length', ''),
                    'groups': row.get('Groups', '').split(', ') if row.get('Groups') else [],
                    'dateAdded': row.get('Date Added', ''),
                    'lastUpdated': row.get('Last Updated', ''),
                    'thumbnail': f'https://i.ytimg.com/vi/{row.get("ID", "")}/hqdefault.jpg',
                    'channelIconUrl': f'https://i.ytimg.com/vi/{row.get("ID", "")}/default.jpg',
                    'timeAgo': ''
                }
                imported_videos.append(video_obj)
        else:
            print(f"Unsupported file format: {filename}")
            return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))

        # Smart Merge Logic for Videos
        videos_added = 0
        for imported_video in imported_videos:
            video_id = imported_video.get('id')
            if not video_id:
                continue

            # Check if video already exists
            if not find_video_by_id(existing_videos, video_id):
                existing_videos.append(imported_video)
                videos_added += 1
                print(f"Added imported video: {imported_video.get('title', video_id)}")
            else:
                print(f"Video {video_id} already exists, skipping...")

        # Smart Merge Logic for Groups
        groups_added = 0
        for imported_group in imported_groups:
            group_id = imported_group.get('id')
            group_name = imported_group.get('name')
            if not group_id or not group_name:
                continue

            # Check if group already exists by ID or name
            existing_group = find_group_by_id(existing_groups, group_id)
            if not existing_group:
                # Also check by name to avoid duplicates
                name_exists = any(g.get('name') == group_name for g in existing_groups)
                if not name_exists:
                    existing_groups.append(imported_group)
                    groups_added += 1
                    print(f"Added imported group: {group_name}")
                else:
                    print(f"Group with name '{group_name}' already exists, skipping...")
            else:
                print(f"Group {group_id} already exists, skipping...")

        # Save merged data
        merged_data = {
            'videos': existing_videos,
            'groups': existing_groups
        }
        save_data(merged_data)

        print(f"Import complete! Added {videos_added} videos and {groups_added} groups.")

    except Exception as e:
        print(f"Error importing data: {e}")

    return redirect(url_for('index', sort_by=current_sort_by, group_filter=current_group_filter))


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
